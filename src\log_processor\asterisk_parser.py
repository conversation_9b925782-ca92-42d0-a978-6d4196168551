# -*- coding: utf-8 -*-
"""
Procesador de logs de Asterisk
Reemplaza la funcionalidad de Logstash para procesar logs de llamadas
"""
import re
import chardet
import json
from typing import Optional, Tuple, List, Dict
from config.settings import LOG_PATTERNS, FILE_CONFIG


class AsteriskLogParser:
    """Clase para procesar logs de Asterisk y extraer información de llamadas"""
    
    def __init__(self, log_file_path: str = None):
        self.log_file_path = log_file_path or FILE_CONFIG['log_file_path']
        self._log_content = None
    
    def _detect_encoding(self, file_path: str) -> str:
        """Detecta la codificación del archivo de logs"""
        try:
            with open(file_path, 'rb') as archivo:
                contenido = archivo.read()
                resultado = chardet.detect(contenido)
                return resultado.get('encoding', 'utf-8')
        except Exception as e:
            print(f"Error detectando codificación: {e}")
            return 'utf-8'
    
    def _read_log_file(self) -> str:
        """Lee el contenido completo del archivo de logs"""
        if self._log_content is not None:
            return self._log_content
            
        try:
            encoding = self._detect_encoding(self.log_file_path)
            print(f"Codificación detectada: {encoding}")
            
            with open(self.log_file_path, 'r', encoding='utf-8', errors='ignore') as archivo:
                self._log_content = archivo.read()
                return self._log_content
                
        except FileNotFoundError:
            print(f"Error: El archivo {self.log_file_path} no se encontró.")
            return ""
        except Exception as e:
            print(f"Error leyendo archivo: {e}")
            return ""
    
    def buscar_llamada_completa(self, numero_caso: str) -> Optional[Tuple[str, str, str]]:
        """
        Busca una llamada completa con calificación en los logs
        
        Args:
            numero_caso: Número de caso a buscar
            
        Returns:
            Tupla (numero, accion, calificacion) o None si no se encuentra
        """
        content = self._read_log_file()
        if not content:
            return None
            
        # Crear patrón específico para este número de caso
        pattern = LOG_PATTERNS['agi_pattern'].format(numero_caso=re.escape(numero_caso))
        
        for line in content.splitlines():
            match = re.search(pattern, line)
            if match:
                numero = match.group(1)
                accion = match.group(2)
                calificacion = match.group(3)
                return (numero, accion, calificacion)
        
        return None
    
    def buscar_llamada_iniciada(self, numero_caso: str) -> Optional[Tuple[str, str]]:
        """
        Busca una llamada que se inició pero puede no tener calificación
        
        Args:
            numero_caso: Número de caso a buscar
            
        Returns:
            Tupla (numero, accion) o None si no se encuentra
        """
        content = self._read_log_file()
        if not content:
            return None
            
        # Crear patrón específico para este número de caso
        pattern = LOG_PATTERNS['call_pattern'].format(numero_caso=re.escape(numero_caso))
        
        for line in content.splitlines():
            match = re.search(pattern, line)
            if match:
                numero = match.group(1)
                accion = match.group(2)
                print(f"Llamada encontrada: {numero}")
                return (numero, accion)
        
        return None
    
    def procesar_todos_los_logs(self) -> List[Dict]:
        """
        Procesa todos los logs y extrae todas las llamadas válidas
        Reemplaza la funcionalidad de Logstash
        
        Returns:
            Lista de diccionarios con los datos extraídos
        """
        content = self._read_log_file()
        if not content:
            return []
            
        resultados = []
        pattern_completo = LOG_PATTERNS['agi_pattern'].replace('{numero_caso}', r'([^\"]+)')
        
        for line_num, line in enumerate(content.splitlines(), 1):
            match = re.search(pattern_completo, line)
            if match:
                resultado = {
                    'numero_caso': match.group(1),
                    'accion': match.group(2),
                    'calificacion': match.group(3),
                    'line_number': line_num,
                    'timestamp': self._extract_timestamp(line)
                }
                resultados.append(resultado)
        
        return resultados
    
    def _extract_timestamp(self, line: str) -> Optional[str]:
        """Extrae timestamp de una línea de log si está disponible"""
        # Patrón básico para timestamp de Asterisk
        timestamp_pattern = r'\[(.*?)\]'
        match = re.search(timestamp_pattern, line)
        return match.group(1) if match else None
    
    def guardar_resultados_json(self, output_path: str = None) -> bool:
        """
        Procesa logs y guarda resultados en formato JSON
        Reemplaza la salida de Logstash
        
        Args:
            output_path: Ruta donde guardar el archivo JSON
            
        Returns:
            True si se guardó exitosamente, False en caso contrario
        """
        output_path = output_path or FILE_CONFIG['results_json_path']
        
        try:
            resultados = self.procesar_todos_los_logs()
            
            with open(output_path, 'w', encoding='utf-8') as archivo:
                for resultado in resultados:
                    json.dump(resultado, archivo, ensure_ascii=False)
                    archivo.write('\n')
            
            print(f"Resultados guardados en: {output_path}")
            print(f"Total de registros procesados: {len(resultados)}")
            return True
            
        except Exception as e:
            print(f"Error guardando resultados: {e}")
            return False
    
    def limpiar_cache(self):
        """Limpia el cache del contenido del archivo"""
        self._log_content = None


# Funciones de compatibilidad con el código existente
def filtrar_logs_y_extraer_variables(log_file_path: str, numero_caso: str) -> Optional[Tuple[str, str, str]]:
    """Función de compatibilidad con el código existente"""
    parser = AsteriskLogParser(log_file_path)
    return parser.buscar_llamada_completa(numero_caso)


def buscar_llamada(log_file_path: str, numero_caso: str) -> Optional[Tuple[str, str]]:
    """Función de compatibilidad con el código existente"""
    parser = AsteriskLogParser(log_file_path)
    return parser.buscar_llamada_iniciada(numero_caso)
