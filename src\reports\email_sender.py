# -*- coding: utf-8 -*-
"""
Sistema de envío de emails para reportes
"""
import smtplib
import ssl
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import base64
import os
from typing import List, Tuple, Optional
from config.settings import EMAIL_CONFIG, get_current_date
from src.utils.file_utils import encode_multiple_files


class EmailSender:
    """Clase para envío de emails con reportes"""
    
    def __init__(self, smtp_server: str = None, smtp_port: int = None, 
                 username: str = None, password: str = None):
        """
        Inicializa el cliente de email
        
        Args:
            smtp_server: Servidor SMTP
            smtp_port: Puerto SMTP
            username: Usuario para autenticación
            password: Contraseña para autenticación
        """
        # Configuración por defecto (se puede sobrescribir con variables de entorno)
        self.smtp_server = smtp_server or os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = smtp_port or int(os.getenv('SMTP_PORT', '587'))
        self.username = username or os.getenv('EMAIL_USERNAME')
        self.password = password or os.getenv('EMAIL_PASSWORD')
        
        if not self.username or not self.password:
            print("Advertencia: Credenciales de email no configuradas")
    
    def send_report(self, 
                   to: str = None,
                   subject: str = None,
                   message: str = None,
                   file_paths: List[str] = None,
                   files_data: List[Tuple[str, str]] = None) -> bool:
        """
        Envía un reporte por email
        
        Args:
            to: Destinatarios (separados por coma)
            subject: Asunto del email
            message: Mensaje del email
            file_paths: Lista de rutas de archivos a adjuntar
            files_data: Lista de tuplas (nombre_archivo, contenido_base64)
            
        Returns:
            True si se envió exitosamente
        """
        # Usar configuración por defecto si no se especifica
        to = to or ','.join(EMAIL_CONFIG['recipients'])
        subject = subject or EMAIL_CONFIG['subject_template'].format(date=get_current_date())
        message = message or EMAIL_CONFIG['message']
        
        if not self.username or not self.password:
            print("Error: Credenciales de email no configuradas")
            return False
        
        try:
            # Crear mensaje
            msg = MIMEMultipart()
            msg['From'] = self.username
            msg['To'] = to
            msg['Subject'] = subject
            
            # Agregar cuerpo del mensaje
            msg.attach(MIMEText(message, 'plain', 'utf-8'))
            
            # Procesar archivos adjuntos
            if file_paths:
                files_data = encode_multiple_files(file_paths)
            
            if files_data:
                for filename, file_content in files_data:
                    self._attach_file(msg, filename, file_content)
            
            # Enviar email
            return self._send_email(msg, to)
            
        except Exception as e:
            print(f"Error enviando email: {e}")
            return False
    
    def _attach_file(self, msg: MIMEMultipart, filename: str, file_content: str):
        """
        Adjunta un archivo al mensaje de email
        
        Args:
            msg: Mensaje de email
            filename: Nombre del archivo
            file_content: Contenido del archivo en base64
        """
        try:
            # Decodificar contenido base64
            file_data = base64.b64decode(file_content)
            
            # Crear adjunto
            part = MIMEBase('application', 'octet-stream')
            part.set_payload(file_data)
            
            # Codificar en base64
            encoders.encode_base64(part)
            
            # Agregar header
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            print(f"Archivo adjuntado: {filename}")
            
        except Exception as e:
            print(f"Error adjuntando archivo {filename}: {e}")
    
    def _send_email(self, msg: MIMEMultipart, to: str) -> bool:
        """
        Envía el mensaje de email
        
        Args:
            msg: Mensaje preparado
            to: Destinatarios
            
        Returns:
            True si se envió exitosamente
        """
        try:
            # Crear contexto SSL
            context = ssl.create_default_context()
            
            # Conectar al servidor
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.username, self.password)
                
                # Enviar mensaje
                text = msg.as_string()
                server.sendmail(self.username, to.split(','), text)
                
                print(f"Email enviado exitosamente a: {to}")
                return True
                
        except Exception as e:
            print(f"Error enviando email: {e}")
            return False
    
    def send_test_email(self, to: str = None) -> bool:
        """
        Envía un email de prueba
        
        Args:
            to: Destinatario de prueba
            
        Returns:
            True si se envió exitosamente
        """
        to = to or EMAIL_CONFIG['recipients'][0]
        
        return self.send_report(
            to=to,
            subject="Test - Sistema de Reportes Voicebot",
            message="Este es un email de prueba del sistema de reportes de Voicebot.\n\nSi recibes este mensaje, la configuración de email está funcionando correctamente."
        )


# Clase de compatibilidad con el código existente
class Mail:
    """Clase de compatibilidad con el código existente"""
    
    def __init__(self):
        self.sender = EmailSender()
    
    def send(self, to: str, subject: str, message: str, files_data: List[Tuple[str, str]] = None) -> bool:
        """
        Método de compatibilidad con la interfaz existente
        
        Args:
            to: Destinatarios
            subject: Asunto
            message: Mensaje
            files_data: Lista de tuplas (nombre_archivo, contenido_base64)
            
        Returns:
            True si se envió exitosamente
        """
        return self.sender.send_report(
            to=to,
            subject=subject,
            message=message,
            files_data=files_data
        )
