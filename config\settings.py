# -*- coding: utf-8 -*-
"""
Configuración del sistema de reportes de Voicebot
"""
import os
from datetime import datetime

# Configuración de MongoDB
MONGODB_CONFIG = {
    'host': '***********',
    'port': 27017,
    'username': 'admin',
    'password': 'V0iC3boatT',
    'database': 'mydatabase'
}

# Configuración de Docker
DOCKER_CONFIG = {
    'container_name': 'asterisk',
    'log_source_path': '/var/log/asterisk/full',
    'log_destination_path': '/tmp/full_log'
}

# Configuración de archivos
FILE_CONFIG = {
    'log_file_path': '/tmp/full_log',
    'results_json_path': '/tmp/resultados.json',
    'output_directory': '/home/<USER>/reporte/',
    'temp_directory': '/tmp/'
}

# Configuración de email
EMAIL_CONFIG = {
    'recipients': [
        '<EMAIL>',
        # '<EMAIL>',
        # '<EMAIL>'
    ],
    'subject_template': 'Reporte {date}',
    'message': 'Reporte Voicebot'
}

# Configuración de reportes
REPORT_CONFIG = {
    'date_format': '%Y-%m-%d',
    'excel_columns': [
        '_id', 'telefono', 'descripcion', 'caso_db', 'nombre', 
        'apellido', 'realizada', 'calificacion', 'finalizo', 
        'reabrir', 'fecha', 'caso', 'asignado', 'grupo'
    ],
    'max_calificacion': 5
}

# Patrones de regex para logs
LOG_PATTERNS = {
    'agi_pattern': r'Executing .* AGI\(\".*\", \"reporte\.py, \"({numero_caso})\", \"([^\"]+)\", \"([0-9]+)\"',
    'call_pattern': r'Executing .* AGI\(\".*\", \"reporte\.py, \"({numero_caso})\", \"([^\"]+)\"'
}

def get_current_date():
    """Obtiene la fecha actual en formato YYYY-MM-DD"""
    return datetime.now().strftime(REPORT_CONFIG['date_format'])

def get_mongodb_uri():
    """Construye la URI de conexión a MongoDB"""
    config = MONGODB_CONFIG
    return f"mongodb://{config['username']}:{config['password']}@{config['host']}:{config['port']}/"
